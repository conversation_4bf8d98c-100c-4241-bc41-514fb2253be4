#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إضافة بيانات تجريبية لبرنامج رصد درجات الطلاب
Sample Data Generator for Student Marks System
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('student_marks.db')
    cursor = conn.cursor()
    
    # إنشاء الجداول إذا لم تكن موجودة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            student_id TEXT UNIQUE NOT NULL,
            class_name TEXT NOT NULL,
            created_date TEXT NOT NULL
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS marks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id TEXT NOT NULL,
            exam_type TEXT NOT NULL,
            subject_part TEXT NOT NULL,
            marks REAL NOT NULL,
            max_marks REAL NOT NULL,
            exam_date TEXT NOT NULL,
            notes TEXT,
            FOREIGN KEY (student_id) REFERENCES students (student_id)
        )
    ''')
    
    # بيانات الطلاب التجريبية
    sample_students = [
        ("أحمد محمد علي", "2023001", "الصف الأول"),
        ("فاطمة أحمد حسن", "2023002", "الصف الأول"),
        ("محمد عبدالله سالم", "2023003", "الصف الثاني"),
        ("عائشة محمود عبدالرحمن", "2023004", "الصف الثاني"),
        ("عبدالرحمن خالد أحمد", "2023005", "الصف الثالث"),
        ("زينب عبدالعزيز محمد", "2023006", "الصف الثالث"),
        ("يوسف إبراهيم علي", "2023007", "الصف الأول"),
        ("مريم سعد عبدالله", "2023008", "الصف الثاني"),
        ("خالد عمر حسن", "2023009", "الصف الثالث"),
        ("نور الدين محمد أحمد", "2023010", "الصف الأول")
    ]
    
    # إضافة الطلاب
    current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    for name, student_id, class_name in sample_students:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO students (name, student_id, class_name, created_date)
                VALUES (?, ?, ?, ?)
            ''', (name, student_id, class_name, current_date))
        except sqlite3.IntegrityError:
            print(f"الطالب {name} موجود مسبقاً")
    
    # أنواع الامتحانات وأجزاء المادة
    exam_types = ["امتحان شهري", "امتحان نصف الفصل", "امتحان نهائي", "واجب منزلي", "مشروع"]
    subject_parts = ["الجزء النظري", "الجزء العملي", "المشروع", "البحث"]
    
    # إضافة درجات تجريبية
    base_date = datetime.now() - timedelta(days=90)
    
    for name, student_id, class_name in sample_students:
        # إضافة عدة درجات لكل طالب
        for i in range(random.randint(3, 8)):
            exam_type = random.choice(exam_types)
            subject_part = random.choice(subject_parts)
            
            # تحديد الدرجة الكاملة حسب نوع الامتحان
            if exam_type == "امتحان نهائي":
                max_marks = random.choice([50, 60, 70, 80, 100])
            elif exam_type == "امتحان نصف الفصل":
                max_marks = random.choice([30, 40, 50])
            elif exam_type == "امتحان شهري":
                max_marks = random.choice([20, 25, 30])
            elif exam_type == "واجب منزلي":
                max_marks = random.choice([10, 15, 20])
            else:  # مشروع
                max_marks = random.choice([25, 30, 40])
            
            # حساب الدرجة المحصلة (بين 60% و 95% من الدرجة الكاملة)
            min_percentage = 0.6
            max_percentage = 0.95
            marks = round(max_marks * random.uniform(min_percentage, max_percentage), 1)
            
            # تاريخ عشوائي خلال الـ 90 يوم الماضية
            exam_date = (base_date + timedelta(days=random.randint(0, 90))).strftime("%Y-%m-%d")
            
            # ملاحظات عشوائية
            notes_options = ["", "ممتاز", "جيد جداً", "يحتاج تحسين", "مشاركة فعالة", ""]
            notes = random.choice(notes_options)
            
            try:
                cursor.execute('''
                    INSERT INTO marks (student_id, exam_type, subject_part, marks, max_marks, exam_date, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (student_id, exam_type, subject_part, marks, max_marks, exam_date, notes))
            except Exception as e:
                print(f"خطأ في إضافة درجة للطالب {name}: {e}")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("تم إنشاء البيانات التجريبية بنجاح!")
    print(f"تم إضافة {len(sample_students)} طلاب مع درجاتهم")

if __name__ == "__main__":
    print("إنشاء بيانات تجريبية لبرنامج رصد درجات الطلاب")
    print("=" * 50)
    
    response = input("هل تريد إنشاء بيانات تجريبية؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        create_sample_data()
    else:
        print("تم إلغاء العملية")
