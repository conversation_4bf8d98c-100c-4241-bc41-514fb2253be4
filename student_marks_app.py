#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج رصد درجات الطلاب في مادة تقنية المعلومات
Student Marks Tracking System for Information Technology Subject
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
import sqlite3

class StudentMarksApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_database()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج رصد درجات الطلاب - مادة تقنية المعلومات")
        self.root.geometry("1200x700")
        self.root.configure(bg='#f0f0f0')
        
        # تعيين اتجاه الكتابة من اليمين إلى اليسار
        self.root.option_add('*Text.direction', 'rtl')
        
        # تعيين الخط العربي
        self.arabic_font = ('Arial Unicode MS', 12)
        self.header_font = ('Arial Unicode MS', 14, 'bold')
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.conn = sqlite3.connect('student_marks.db')
        self.cursor = self.conn.cursor()
        
        # إنشاء جدول الطلاب
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                student_id TEXT UNIQUE NOT NULL,
                class_name TEXT NOT NULL,
                created_date TEXT NOT NULL
            )
        ''')
        
        # إنشاء جدول الدرجات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS marks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id TEXT NOT NULL,
                exam_type TEXT NOT NULL,
                subject_part TEXT NOT NULL,
                marks REAL NOT NULL,
                max_marks REAL NOT NULL,
                exam_date TEXT NOT NULL,
                notes TEXT,
                FOREIGN KEY (student_id) REFERENCES students (student_id)
            )
        ''')
        
        self.conn.commit()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # العنوان الرئيسي
        title_label = tk.Label(main_frame, text="برنامج رصد درجات الطلاب - مادة تقنية المعلومات", 
                              font=self.header_font, bg='#f0f0f0', fg='#2c3e50')
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20))
        
        # إطار إدخال بيانات الطالب
        student_frame = ttk.LabelFrame(main_frame, text="بيانات الطالب", padding="10")
        student_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # اسم الطالب
        tk.Label(student_frame, text="اسم الطالب:", font=self.arabic_font).grid(row=0, column=1, sticky=tk.E, padx=5)
        self.student_name_var = tk.StringVar()
        self.student_name_entry = tk.Entry(student_frame, textvariable=self.student_name_var, 
                                          font=self.arabic_font, width=25, justify='right')
        self.student_name_entry.grid(row=0, column=0, padx=5, pady=2)
        
        # رقم الطالب
        tk.Label(student_frame, text="رقم الطالب:", font=self.arabic_font).grid(row=1, column=1, sticky=tk.E, padx=5)
        self.student_id_var = tk.StringVar()
        self.student_id_entry = tk.Entry(student_frame, textvariable=self.student_id_var, 
                                        font=self.arabic_font, width=25, justify='right')
        self.student_id_entry.grid(row=1, column=0, padx=5, pady=2)
        
        # الصف
        tk.Label(student_frame, text="الصف:", font=self.arabic_font).grid(row=2, column=1, sticky=tk.E, padx=5)
        self.class_var = tk.StringVar()
        class_combo = ttk.Combobox(student_frame, textvariable=self.class_var, 
                                  values=["الصف الأول", "الصف الثاني", "الصف الثالث"], 
                                  font=self.arabic_font, width=22, justify='right')
        class_combo.grid(row=2, column=0, padx=5, pady=2)
        
        # أزرار إدارة الطلاب
        buttons_frame = tk.Frame(student_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        tk.Button(buttons_frame, text="إضافة طالب", command=self.add_student, 
                 font=self.arabic_font, bg='#27ae60', fg='white', width=12).pack(side=tk.RIGHT, padx=2)
        tk.Button(buttons_frame, text="تحديث بيانات", command=self.update_student, 
                 font=self.arabic_font, bg='#f39c12', fg='white', width=12).pack(side=tk.RIGHT, padx=2)
        tk.Button(buttons_frame, text="حذف طالب", command=self.delete_student, 
                 font=self.arabic_font, bg='#e74c3c', fg='white', width=12).pack(side=tk.RIGHT, padx=2)
        
        # إطار إدخال الدرجات
        marks_frame = ttk.LabelFrame(main_frame, text="إدخال الدرجات", padding="10")
        marks_frame.grid(row=1, column=2, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # نوع الامتحان
        tk.Label(marks_frame, text="نوع الامتحان:", font=self.arabic_font).grid(row=0, column=1, sticky=tk.E, padx=5)
        self.exam_type_var = tk.StringVar()
        exam_combo = ttk.Combobox(marks_frame, textvariable=self.exam_type_var,
                                 values=["امتحان شهري", "امتحان نصف الفصل", "امتحان نهائي", "واجب منزلي", "مشروع"],
                                 font=self.arabic_font, width=22, justify='right')
        exam_combo.grid(row=0, column=0, padx=5, pady=2)
        
        # جزء المادة
        tk.Label(marks_frame, text="جزء المادة:", font=self.arabic_font).grid(row=1, column=1, sticky=tk.E, padx=5)
        self.subject_part_var = tk.StringVar()
        part_combo = ttk.Combobox(marks_frame, textvariable=self.subject_part_var,
                                 values=["الجزء النظري", "الجزء العملي", "المشروع", "البحث"],
                                 font=self.arabic_font, width=22, justify='right')
        part_combo.grid(row=1, column=0, padx=5, pady=2)
        
        # الدرجة المحصلة
        tk.Label(marks_frame, text="الدرجة المحصلة:", font=self.arabic_font).grid(row=2, column=1, sticky=tk.E, padx=5)
        self.marks_var = tk.StringVar()
        self.marks_entry = tk.Entry(marks_frame, textvariable=self.marks_var, 
                                   font=self.arabic_font, width=25, justify='right')
        self.marks_entry.grid(row=2, column=0, padx=5, pady=2)
        
        # الدرجة الكاملة
        tk.Label(marks_frame, text="الدرجة الكاملة:", font=self.arabic_font).grid(row=3, column=1, sticky=tk.E, padx=5)
        self.max_marks_var = tk.StringVar()
        self.max_marks_entry = tk.Entry(marks_frame, textvariable=self.max_marks_var, 
                                       font=self.arabic_font, width=25, justify='right')
        self.max_marks_entry.grid(row=3, column=0, padx=5, pady=2)
        
        # تاريخ الامتحان
        tk.Label(marks_frame, text="تاريخ الامتحان:", font=self.arabic_font).grid(row=4, column=1, sticky=tk.E, padx=5)
        self.exam_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.exam_date_entry = tk.Entry(marks_frame, textvariable=self.exam_date_var, 
                                       font=self.arabic_font, width=25, justify='right')
        self.exam_date_entry.grid(row=4, column=0, padx=5, pady=2)
        
        # ملاحظات
        tk.Label(marks_frame, text="ملاحظات:", font=self.arabic_font).grid(row=5, column=1, sticky=tk.E, padx=5)
        self.notes_var = tk.StringVar()
        self.notes_entry = tk.Entry(marks_frame, textvariable=self.notes_var, 
                                   font=self.arabic_font, width=25, justify='right')
        self.notes_entry.grid(row=5, column=0, padx=5, pady=2)
        
        # أزرار إدارة الدرجات
        marks_buttons_frame = tk.Frame(marks_frame)
        marks_buttons_frame.grid(row=6, column=0, columnspan=2, pady=10)
        
        tk.Button(marks_buttons_frame, text="إضافة درجة", command=self.add_marks, 
                 font=self.arabic_font, bg='#3498db', fg='white', width=12).pack(side=tk.RIGHT, padx=2)
        tk.Button(marks_buttons_frame, text="حذف درجة", command=self.delete_marks, 
                 font=self.arabic_font, bg='#e74c3c', fg='white', width=12).pack(side=tk.RIGHT, padx=2)
        
        # جدول عرض البيانات
        self.create_treeview(main_frame)
        
        # أزرار إضافية
        self.create_additional_buttons(main_frame)
    
    def create_treeview(self, parent):
        """إنشاء جدول عرض البيانات"""
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=20)
        
        # إنشاء Treeview
        columns = ('student_name', 'student_id', 'class_name', 'exam_type', 'subject_part', 
                  'marks', 'max_marks', 'percentage', 'exam_date', 'notes')
        
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        self.tree.heading('student_name', text='اسم الطالب')
        self.tree.heading('student_id', text='رقم الطالب')
        self.tree.heading('class_name', text='الصف')
        self.tree.heading('exam_type', text='نوع الامتحان')
        self.tree.heading('subject_part', text='جزء المادة')
        self.tree.heading('marks', text='الدرجة')
        self.tree.heading('max_marks', text='الدرجة الكاملة')
        self.tree.heading('percentage', text='النسبة المئوية')
        self.tree.heading('exam_date', text='تاريخ الامتحان')
        self.tree.heading('notes', text='ملاحظات')
        
        # تعيين عرض الأعمدة
        for col in columns:
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # ربط حدث النقر
        self.tree.bind('<ButtonRelease-1>', self.on_tree_select)
    
    def create_additional_buttons(self, parent):
        """إنشاء أزرار إضافية"""
        buttons_frame = tk.Frame(parent, bg='#f0f0f0')
        buttons_frame.grid(row=3, column=0, columnspan=4, pady=20)
        
        tk.Button(buttons_frame, text="تقرير شامل", command=self.generate_report, 
                 font=self.arabic_font, bg='#9b59b6', fg='white', width=15).pack(side=tk.RIGHT, padx=5)
        tk.Button(buttons_frame, text="تصدير إلى Excel", command=self.export_to_excel, 
                 font=self.arabic_font, bg='#1abc9c', fg='white', width=15).pack(side=tk.RIGHT, padx=5)
        tk.Button(buttons_frame, text="نسخ احتياطي", command=self.backup_data, 
                 font=self.arabic_font, bg='#34495e', fg='white', width=15).pack(side=tk.RIGHT, padx=5)
        tk.Button(buttons_frame, text="استعادة البيانات", command=self.restore_data,
                 font=self.arabic_font, bg='#e67e22', fg='white', width=15).pack(side=tk.RIGHT, padx=5)

    def add_student(self):
        """إضافة طالب جديد"""
        name = self.student_name_var.get().strip()
        student_id = self.student_id_var.get().strip()
        class_name = self.class_var.get().strip()

        if not all([name, student_id, class_name]):
            messagebox.showerror("خطأ", "يرجى ملء جميع البيانات المطلوبة")
            return

        try:
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cursor.execute('''
                INSERT INTO students (name, student_id, class_name, created_date)
                VALUES (?, ?, ?, ?)
            ''', (name, student_id, class_name, current_date))
            self.conn.commit()

            messagebox.showinfo("نجح", f"تم إضافة الطالب {name} بنجاح")
            self.clear_student_fields()
            self.load_data()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "رقم الطالب موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def update_student(self):
        """تحديث بيانات الطالب"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب من القائمة")
            return

        name = self.student_name_var.get().strip()
        student_id = self.student_id_var.get().strip()
        class_name = self.class_var.get().strip()

        if not all([name, student_id, class_name]):
            messagebox.showerror("خطأ", "يرجى ملء جميع البيانات المطلوبة")
            return

        try:
            # الحصول على رقم الطالب الحالي من الجدول
            item_values = self.tree.item(selected_item[0])['values']
            old_student_id = item_values[1]

            self.cursor.execute('''
                UPDATE students SET name=?, student_id=?, class_name=?
                WHERE student_id=?
            ''', (name, student_id, class_name, old_student_id))

            # تحديث رقم الطالب في جدول الدرجات أيضاً
            if old_student_id != student_id:
                self.cursor.execute('''
                    UPDATE marks SET student_id=? WHERE student_id=?
                ''', (student_id, old_student_id))

            self.conn.commit()
            messagebox.showinfo("نجح", "تم تحديث بيانات الطالب بنجاح")
            self.clear_student_fields()
            self.load_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_student(self):
        """حذف طالب"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب من القائمة")
            return

        item_values = self.tree.item(selected_item[0])['values']
        student_name = item_values[0]
        student_id = item_values[1]

        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الطالب {student_name}؟\nسيتم حذف جميع درجاته أيضاً")

        if result:
            try:
                # حذف الدرجات أولاً
                self.cursor.execute('DELETE FROM marks WHERE student_id=?', (student_id,))
                # ثم حذف الطالب
                self.cursor.execute('DELETE FROM students WHERE student_id=?', (student_id,))
                self.conn.commit()

                messagebox.showinfo("نجح", f"تم حذف الطالب {student_name} بنجاح")
                self.clear_student_fields()
                self.load_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def add_marks(self):
        """إضافة درجة جديدة"""
        student_id = self.student_id_var.get().strip()
        exam_type = self.exam_type_var.get().strip()
        subject_part = self.subject_part_var.get().strip()
        marks = self.marks_var.get().strip()
        max_marks = self.max_marks_var.get().strip()
        exam_date = self.exam_date_var.get().strip()
        notes = self.notes_var.get().strip()

        if not all([student_id, exam_type, subject_part, marks, max_marks, exam_date]):
            messagebox.showerror("خطأ", "يرجى ملء جميع البيانات المطلوبة")
            return

        try:
            marks_float = float(marks)
            max_marks_float = float(max_marks)

            if marks_float > max_marks_float:
                messagebox.showerror("خطأ", "الدرجة المحصلة لا يمكن أن تكون أكبر من الدرجة الكاملة")
                return

            if marks_float < 0 or max_marks_float <= 0:
                messagebox.showerror("خطأ", "الدرجات يجب أن تكون أرقام موجبة")
                return

            # التحقق من وجود الطالب
            self.cursor.execute('SELECT name FROM students WHERE student_id=?', (student_id,))
            student = self.cursor.fetchone()

            if not student:
                messagebox.showerror("خطأ", "رقم الطالب غير موجود")
                return

            self.cursor.execute('''
                INSERT INTO marks (student_id, exam_type, subject_part, marks, max_marks, exam_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (student_id, exam_type, subject_part, marks_float, max_marks_float, exam_date, notes))

            self.conn.commit()
            messagebox.showinfo("نجح", f"تم إضافة درجة للطالب {student[0]} بنجاح")
            self.clear_marks_fields()
            self.load_data()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للدرجات")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_marks(self):
        """حذف درجة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار درجة من القائمة")
            return

        item_values = self.tree.item(selected_item[0])['values']
        student_name = item_values[0]
        student_id = item_values[1]
        exam_type = item_values[3]
        subject_part = item_values[4]

        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف درجة {exam_type} - {subject_part} للطالب {student_name}؟")

        if result:
            try:
                self.cursor.execute('''
                    DELETE FROM marks
                    WHERE student_id=? AND exam_type=? AND subject_part=?
                ''', (student_id, exam_type, subject_part))

                self.conn.commit()
                messagebox.showinfo("نجح", "تم حذف الدرجة بنجاح")
                self.load_data()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def on_tree_select(self, event):
        """عند اختيار عنصر من الجدول"""
        selected_item = self.tree.selection()
        if selected_item:
            item_values = self.tree.item(selected_item[0])['values']

            # ملء بيانات الطالب
            self.student_name_var.set(item_values[0])
            self.student_id_var.set(item_values[1])
            self.class_var.set(item_values[2])

            # ملء بيانات الدرجة إذا كانت متوفرة
            if len(item_values) > 3 and item_values[3]:
                self.exam_type_var.set(item_values[3])
                self.subject_part_var.set(item_values[4])
                self.marks_var.set(item_values[5])
                self.max_marks_var.set(item_values[6])
                self.exam_date_var.set(item_values[8])
                self.notes_var.set(item_values[9] if item_values[9] else "")

    def load_data(self):
        """تحميل البيانات في الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        # تحميل البيانات من قاعدة البيانات
        self.cursor.execute('''
            SELECT s.name, s.student_id, s.class_name,
                   m.exam_type, m.subject_part, m.marks, m.max_marks,
                   m.exam_date, m.notes
            FROM students s
            LEFT JOIN marks m ON s.student_id = m.student_id
            ORDER BY s.name, m.exam_date DESC
        ''')

        rows = self.cursor.fetchall()

        for row in rows:
            # حساب النسبة المئوية
            if row[5] is not None and row[6] is not None and row[6] > 0:
                percentage = f"{(row[5] / row[6]) * 100:.1f}%"
            else:
                percentage = ""

            # تحويل الأرقام إلى الأرقام الهندية
            display_row = list(row)
            if row[5] is not None:
                display_row[5] = self.convert_to_hindi_numbers(str(row[5]))
            if row[6] is not None:
                display_row[6] = self.convert_to_hindi_numbers(str(row[6]))

            # إدراج البيانات مع النسبة المئوية
            self.tree.insert('', 'end', values=(*display_row[:7], percentage, *display_row[7:]))

    def convert_to_hindi_numbers(self, text):
        """تحويل الأرقام الإنجليزية إلى الأرقام الهندية"""
        hindi_digits = '٠١٢٣٤٥٦٧٨٩'
        english_digits = '0123456789'

        for i, digit in enumerate(english_digits):
            text = text.replace(digit, hindi_digits[i])

        return text

    def clear_student_fields(self):
        """مسح حقول بيانات الطالب"""
        self.student_name_var.set("")
        self.student_id_var.set("")
        self.class_var.set("")

    def clear_marks_fields(self):
        """مسح حقول الدرجات"""
        self.exam_type_var.set("")
        self.subject_part_var.set("")
        self.marks_var.set("")
        self.max_marks_var.set("")
        self.exam_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.notes_var.set("")

    def generate_report(self):
        """إنشاء تقرير شامل"""
        report_window = tk.Toplevel(self.root)
        report_window.title("التقرير الشامل")
        report_window.geometry("800x600")
        report_window.configure(bg='#f0f0f0')

        # إطار التقرير
        report_frame = ttk.Frame(report_window, padding="20")
        report_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان التقرير
        title_label = tk.Label(report_frame, text="التقرير الشامل لدرجات الطلاب",
                              font=self.header_font, bg='#f0f0f0')
        title_label.pack(pady=(0, 20))

        # منطقة النص
        text_frame = tk.Frame(report_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, font=self.arabic_font, wrap=tk.WORD,
                             bg='white', fg='black', direction='rtl')
        scrollbar_report = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar_report.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_report.pack(side=tk.RIGHT, fill=tk.Y)

        # إنشاء التقرير
        report_content = self.create_report_content()
        text_widget.insert(tk.END, report_content)
        text_widget.config(state=tk.DISABLED)

        # أزرار التقرير
        buttons_frame = tk.Frame(report_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="طباعة التقرير", command=lambda: self.print_report(report_content),
                 font=self.arabic_font, bg='#3498db', fg='white').pack(side=tk.RIGHT, padx=5)
        tk.Button(buttons_frame, text="حفظ التقرير", command=lambda: self.save_report(report_content),
                 font=self.arabic_font, bg='#27ae60', fg='white').pack(side=tk.RIGHT, padx=5)

    def create_report_content(self):
        """إنشاء محتوى التقرير"""
        current_date = datetime.now().strftime("%Y-%m-%d")

        report = f"""
تقرير شامل لدرجات الطلاب في مادة تقنية المعلومات
تاريخ التقرير: {current_date}
{'='*60}

إحصائيات عامة:
"""

        # إحصائيات الطلاب
        self.cursor.execute('SELECT COUNT(*) FROM students')
        total_students = self.cursor.fetchone()[0]

        self.cursor.execute('SELECT COUNT(*) FROM marks')
        total_marks = self.cursor.fetchone()[0]

        report += f"""
عدد الطلاب المسجلين: {self.convert_to_hindi_numbers(str(total_students))}
عدد الدرجات المسجلة: {self.convert_to_hindi_numbers(str(total_marks))}

تفاصيل الطلاب والدرجات:
{'='*60}
"""

        # تفاصيل كل طالب
        self.cursor.execute('''
            SELECT s.name, s.student_id, s.class_name,
                   COUNT(m.id) as exam_count,
                   AVG(m.marks * 100.0 / m.max_marks) as avg_percentage
            FROM students s
            LEFT JOIN marks m ON s.student_id = m.student_id
            GROUP BY s.student_id
            ORDER BY s.name
        ''')

        students_data = self.cursor.fetchall()

        for student in students_data:
            name, student_id, class_name, exam_count, avg_percentage = student

            report += f"""
الطالب: {name}
رقم الطالب: {self.convert_to_hindi_numbers(student_id)}
الصف: {class_name}
عدد الامتحانات: {self.convert_to_hindi_numbers(str(exam_count))}
"""

            if avg_percentage:
                report += f"المعدل العام: {self.convert_to_hindi_numbers(f'{avg_percentage:.1f}')}%\n"
            else:
                report += "المعدل العام: لا توجد درجات\n"

            # درجات الطالب
            self.cursor.execute('''
                SELECT exam_type, subject_part, marks, max_marks, exam_date
                FROM marks
                WHERE student_id = ?
                ORDER BY exam_date DESC
            ''', (student_id,))

            marks_data = self.cursor.fetchall()

            if marks_data:
                report += "الدرجات:\n"
                for mark in marks_data:
                    exam_type, subject_part, marks, max_marks, exam_date = mark
                    percentage = (marks / max_marks) * 100
                    report += f"  - {exam_type} ({subject_part}): {self.convert_to_hindi_numbers(str(marks))}/{self.convert_to_hindi_numbers(str(max_marks))} ({self.convert_to_hindi_numbers(f'{percentage:.1f}')}%) - {exam_date}\n"

            report += "-" * 40 + "\n"

        return report

    def save_report(self, content):
        """حفظ التقرير"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="حفظ التقرير"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في: {filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, content):
        """طباعة التقرير"""
        messagebox.showinfo("طباعة", "يمكنك نسخ محتوى التقرير وطباعته من أي برنامج معالج نصوص")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd

            # جمع البيانات
            self.cursor.execute('''
                SELECT s.name, s.student_id, s.class_name,
                       m.exam_type, m.subject_part, m.marks, m.max_marks,
                       (m.marks * 100.0 / m.max_marks) as percentage,
                       m.exam_date, m.notes
                FROM students s
                LEFT JOIN marks m ON s.student_id = m.student_id
                ORDER BY s.name, m.exam_date DESC
            ''')

            data = self.cursor.fetchall()
            columns = ['اسم الطالب', 'رقم الطالب', 'الصف', 'نوع الامتحان', 'جزء المادة',
                      'الدرجة', 'الدرجة الكاملة', 'النسبة المئوية', 'تاريخ الامتحان', 'ملاحظات']

            df = pd.DataFrame(data, columns=columns)

            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="تصدير إلى Excel"
            )

            if filename:
                df.to_excel(filename, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {filename}")

        except ImportError:
            messagebox.showerror("خطأ", "يرجى تثبيت مكتبة pandas و openpyxl لتصدير البيانات إلى Excel")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def backup_data(self):
        """إنشاء نسخة احتياطية"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="حفظ النسخة الاحتياطية"
        )

        if filename:
            try:
                # جمع بيانات الطلاب
                self.cursor.execute('SELECT * FROM students')
                students = self.cursor.fetchall()

                # جمع بيانات الدرجات
                self.cursor.execute('SELECT * FROM marks')
                marks = self.cursor.fetchall()

                backup_data = {
                    'students': students,
                    'marks': marks,
                    'backup_date': datetime.now().isoformat()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية: {filename}")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_data(self):
        """استعادة البيانات من النسخة الاحتياطية"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="اختيار النسخة الاحتياطية"
        )

        if filename:
            result = messagebox.askyesno("تأكيد الاستعادة",
                                       "هل أنت متأكد من استعادة البيانات؟\nسيتم حذف جميع البيانات الحالية")

            if result:
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    # حذف البيانات الحالية
                    self.cursor.execute('DELETE FROM marks')
                    self.cursor.execute('DELETE FROM students')

                    # استعادة بيانات الطلاب
                    for student in backup_data['students']:
                        self.cursor.execute('''
                            INSERT INTO students (id, name, student_id, class_name, created_date)
                            VALUES (?, ?, ?, ?, ?)
                        ''', student)

                    # استعادة بيانات الدرجات
                    for mark in backup_data['marks']:
                        self.cursor.execute('''
                            INSERT INTO marks (id, student_id, exam_type, subject_part, marks, max_marks, exam_date, notes)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', mark)

                    self.conn.commit()
                    self.load_data()

                    messagebox.showinfo("نجح", "تم استعادة البيانات بنجاح")

                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}")

    def __del__(self):
        """إغلاق اتصال قاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = StudentMarksApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
