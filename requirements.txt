# متطلبات برنامج رصد درجات الطلاب
# Student Marks Tracking System Requirements

# المكتبات الأساسية - Basic Libraries
# tkinter - مدمج مع Python (Built-in with Python)

# مكتبات إضافية اختيارية - Optional Additional Libraries
pandas>=1.3.0
openpyxl>=3.0.0

# ملاحظات - Notes:
# tkinter: مكتبة الواجهة الرسومية (مدمجة مع Python)
# sqlite3: قاعدة البيانات (مدمجة مع Python)
# json: للنسخ الاحتياطية (مدمجة مع Python)
# datetime: للتواريخ (مدمجة مع Python)
# pandas: لتصدير البيانات إلى Excel (اختيارية)
# openpyxl: لدعم ملفات Excel (اختيارية)
