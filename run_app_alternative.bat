@echo off
chcp 65001
title برنامج رصد درجات الطلاب
echo ================================================
echo برنامج رصد درجات الطلاب - مادة تقنية المعلومات
echo ================================================
echo.

echo جاري البحث عن Python...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة
echo المحاولة 1: python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على Python
    echo جاري تشغيل البرنامج...
    python student_marks_app.py
    goto :end
)

echo المحاولة 2: py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على Python
    echo جاري تشغيل البرنامج...
    py student_marks_app.py
    goto :end
)

echo المحاولة 3: python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على Python
    echo جاري تشغيل البرنامج...
    python3 student_marks_app.py
    goto :end
)

REM إذا لم يتم العثور على Python
echo.
echo ================================================
echo خطأ: لم يتم العثور على Python
echo ================================================
echo.
echo يرجى تثبيت Python أولاً من:
echo https://www.python.org/downloads/
echo.
echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
echo.
echo بعد التثبيت، أعد تشغيل هذا الملف
echo.

:end
echo.
echo ================================================
pause
