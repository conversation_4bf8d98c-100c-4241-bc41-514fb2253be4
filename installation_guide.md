# دليل التثبيت والتشغيل - برنامج رصد درجات الطلاب

## خطوات التثبيت

### 1. تثبيت Python

#### للمستخدمين الجدد:
1. اذه<PERSON> إلى الموقع الرسمي: https://www.python.org/downloads/
2. حمل أحدث إصدار من Python (3.8 أو أحدث)
3. شغل ملف التثبيت
4. **مهم جداً**: تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت
5. اتبع خطوات التثبيت العادية

#### للتحقق من تثبيت Python:
افتح Command Prompt أو PowerShell واكتب:
```cmd
python --version
```
أو
```cmd
py --version
```

### 2. تثبيت المكتبات المطلوبة

افتح Command Prompt في مجلد البرنامج واكتب:
```cmd
pip install pandas openpyxl
```

### 3. تشغيل البرنامج

#### الطريقة الأولى - من Command Prompt:
```cmd
python student_marks_app.py
```

#### الطريقة الثانية - ملف التشغيل السريع:
انقر نقراً مزدوجاً على ملف `run_app.bat`

#### الطريقة الثالثة - من Python IDLE:
1. افتح Python IDLE
2. اختر File > Open
3. اختر ملف `student_marks_app.py`
4. اضغط F5 أو اختر Run > Run Module

## إضافة بيانات تجريبية

لإضافة بيانات تجريبية للاختبار:
```cmd
python sample_data.py
```

## حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**الحل:**
1. تأكد من تثبيت Python بشكل صحيح
2. أضف Python إلى PATH يدوياً:
   - اذهب إلى Control Panel > System > Advanced System Settings
   - اضغط على Environment Variables
   - في System Variables، ابحث عن PATH
   - أضف مسار Python (عادة: C:\Python39\ أو C:\Users\<USER>\AppData\Local\Programs\Python\Python39\)

### مشكلة: "No module named 'pandas'"
**الحل:**
```cmd
pip install pandas openpyxl
```

### مشكلة: "No module named 'tkinter'"
**الحل:**
tkinter مدمج مع Python، إذا لم يعمل:
- تأكد من تثبيت Python الكامل (وليس Python من Microsoft Store)
- أعد تثبيت Python مع تحديد جميع المكونات

### مشكلة: الخطوط العربية لا تظهر بشكل صحيح
**الحل:**
- تأكد من أن نظام التشغيل يدعم اللغة العربية
- قم بتثبيت خطوط عربية إضافية إذا لزم الأمر

## متطلبات النظام

### الحد الأدنى:
- نظام التشغيل: Windows 7/8/10/11, macOS 10.12+, أو Linux
- Python 3.6 أو أحدث
- ذاكرة: 512 MB RAM
- مساحة القرص: 100 MB

### المستحسن:
- Python 3.8 أو أحدث
- ذاكرة: 1 GB RAM أو أكثر
- دقة الشاشة: 1024x768 أو أعلى

## الملفات المهمة

### الملفات الأساسية:
- `student_marks_app.py` - الملف الرئيسي للبرنامج
- `requirements.txt` - قائمة المكتبات المطلوبة
- `README.md` - دليل الاستخدام

### الملفات المساعدة:
- `sample_data.py` - إنشاء بيانات تجريبية
- `run_app.bat` - ملف تشغيل سريع لـ Windows
- `installation_guide.md` - هذا الدليل

### الملفات المُنشأة تلقائياً:
- `student_marks.db` - قاعدة البيانات
- ملفات النسخ الاحتياطية (.json)
- ملفات التقارير (.txt)
- ملفات Excel المُصدرة (.xlsx)

## الدعم الفني

### للمساعدة:
1. تأكد من قراءة هذا الدليل كاملاً
2. تحقق من رسائل الخطأ في البرنامج
3. تأكد من تثبيت جميع المتطلبات

### نصائح للاستخدام الأمثل:
1. قم بعمل نسخة احتياطية دورية من البيانات
2. استخدم أرقام طلاب فريدة لتجنب التضارب
3. تأكد من إدخال التواريخ بالصيغة الصحيحة (YYYY-MM-DD)
4. احفظ التقارير المهمة في ملفات منفصلة

## ترخيص الاستخدام
هذا البرنامج مجاني للاستخدام التعليمي والشخصي.
