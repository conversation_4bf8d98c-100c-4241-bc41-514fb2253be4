🎓 تعليمات تشغيل برنامج رصد درجات الطلاب
================================================

❌ المشكلة الحالية:
Python غير مثبت بشكل صحيح أو غير متوافق مع نظام التشغيل

✅ الحلول المقترحة:

الحل الأول - تثبيت Python الصحيح:
=====================================

1. اذهب إلى: https://www.python.org/downloads/
2. اختر "Download Python 3.11.x" (أحدث إصدار مستقر)
3. تأكد من اختيار النسخة المناسبة لنظام التشغيل:
   - Windows x86-64 (للأنظمة 64-bit)
   - Windows x86 (للأنظمة 32-bit)

4. أثناء التثبيت:
   ✅ تأكد من تحديد "Add Python to PATH"
   ✅ اختر "Install for all users"
   ✅ اختر "Customize installation"
   ✅ تأكد من تحديد "tkinter/Tk" في Optional Features

5. بعد التثبيت، أعد تشغيل الكمبيوتر

6. افتح Command Prompt واكتب:
   python --version
   (يجب أن يظهر رقم الإصدار)

الحل الثاني - استخدام Python من Microsoft Store:
===============================================

1. افتح Microsoft Store
2. ابحث عن "Python 3.11"
3. اضغط "Get" أو "Install"
4. بعد التثبيت، جرب الأمر: python --version

الحل الثالث - تشغيل مباشر من IDLE:
==================================

1. ابحث في قائمة Start عن "IDLE"
2. افتح "IDLE (Python 3.x)"
3. في IDLE، اختر File > Open
4. اختر ملف "student_marks_app.py"
5. اضغط F5 أو Run > Run Module

الحل الرابع - استخدام PyInstaller (إنشاء ملف تنفيذي):
================================================

إذا كان Python مثبت ولكن لا يعمل من Command Line:

1. افتح Command Prompt كـ Administrator
2. اكتب: pip install pyinstaller
3. انتقل إلى مجلد البرنامج
4. اكتب: pyinstaller --onefile --windowed student_marks_app.py
5. ستجد ملف .exe في مجلد dist

طرق التشغيل البديلة:
==================

إذا كان Python مثبت:
- انقر نقراً مزدوجاً على run_app_alternative.bat
- أو افتح PowerShell في مجلد البرنامج واكتب: python student_marks_app.py

التحقق من التثبيت:
================

افتح Command Prompt واكتب هذه الأوامر:
1. python --version
2. python -c "import tkinter; print('tkinter works!')"
3. python -c "import sqlite3; print('sqlite3 works!')"

إذا ظهرت رسائل خطأ، فهناك مشكلة في التثبيت.

استكشاف الأخطاء:
===============

خطأ: "python is not recognized"
الحل: أضف Python إلى PATH يدوياً

خطأ: "No module named tkinter"
الحل: أعد تثبيت Python مع تحديد tkinter

خطأ: "Access denied"
الحل: شغل Command Prompt كـ Administrator

للمساعدة الإضافية:
================

1. تأكد من أن نظام التشغيل محدث
2. أغلق برامج الحماية مؤقتاً أثناء التثبيت
3. جرب تثبيت Python في مجلد مختلف
4. استخدم "Run as Administrator" عند التثبيت

بعد حل مشكلة Python، ستظهر واجهة البرنامج العربية بشكل طبيعي! 🎉
