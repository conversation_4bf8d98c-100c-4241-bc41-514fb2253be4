@echo off
chcp 65001
title تثبيت المكتبات المطلوبة
echo ================================================
echo تثبيت المكتبات المطلوبة لبرنامج رصد الدرجات
echo ================================================
echo.

echo جاري تثبيت المكتبات...
echo.

REM محاولة تثبيت المكتبات بطرق مختلفة
pip --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على pip
    echo جاري تثبيت pandas...
    pip install pandas
    echo جاري تثبيت openpyxl...
    pip install openpyxl
    echo.
    echo تم تثبيت جميع المكتبات بنجاح!
    goto :end
)

python -m pip --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على pip
    echo جاري تثبيت pandas...
    python -m pip install pandas
    echo جاري تثبيت openpyxl...
    python -m pip install openpyxl
    echo.
    echo تم تثبيت جميع المكتبات بنجاح!
    goto :end
)

py -m pip --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على pip
    echo جاري تثبيت pandas...
    py -m pip install pandas
    echo جاري تثبيت openpyxl...
    py -m pip install openpyxl
    echo.
    echo تم تثبيت جميع المكتبات بنجاح!
    goto :end
)

echo خطأ: لم يتم العثور على pip
echo يرجى تثبيت Python أولاً

:end
echo.
echo ================================================
echo ملاحظة: هذه المكتبات اختيارية لتصدير البيانات إلى Excel
echo البرنامج سيعمل بدونها ولكن بدون ميزة التصدير
echo ================================================
pause
