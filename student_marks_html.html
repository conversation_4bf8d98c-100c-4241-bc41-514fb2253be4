<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج رصد درجات الطلاب - مادة تقنية المعلومات</title>
    <style>
        * {
            box-sizing: border-box;
            font-family: 'Arial Unicode MS', 'Tahoma', sans-serif;
        }
        
        body {
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .form-section {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .form-group {
            flex: 1;
            min-width: 300px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .form-group h3 {
            color: #34495e;
            margin-top: 0;
            text-align: center;
            background: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
        }
        
        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 10px;
        }
        
        .form-row label {
            min-width: 120px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-row input, .form-row select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            text-align: right;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #9b59b6; color: white; }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .data-table th, .data-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background: #34495e;
            color: white;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .stat-card {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .form-section {
                flex-direction: column;
            }
            
            .form-group {
                min-width: auto;
            }
            
            .stats {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>برنامج رصد درجات الطلاب</h1>
            <h2>مادة تقنية المعلومات</h2>
            <p>نسخة ويب تجريبية - تعمل في المتصفح بدون تثبيت</p>
        </div>
        
        <div id="alerts"></div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalStudents">٠</div>
                <div>إجمالي الطلاب</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMarks">٠</div>
                <div>إجمالي الدرجات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgGrade">٠٪</div>
                <div>المعدل العام</div>
            </div>
        </div>
        
        <div class="form-section">
            <div class="form-group">
                <h3>بيانات الطالب</h3>
                
                <div class="form-row">
                    <label>اسم الطالب:</label>
                    <input type="text" id="studentName" placeholder="أدخل اسم الطالب">
                </div>
                
                <div class="form-row">
                    <label>رقم الطالب:</label>
                    <input type="text" id="studentId" placeholder="أدخل رقم الطالب">
                </div>
                
                <div class="form-row">
                    <label>الصف:</label>
                    <select id="className">
                        <option value="">اختر الصف</option>
                        <option value="الصف الأول">الصف الأول</option>
                        <option value="الصف الثاني">الصف الثاني</option>
                        <option value="الصف الثالث">الصف الثالث</option>
                    </select>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="addStudent()">إضافة طالب</button>
                    <button class="btn btn-warning" onclick="updateStudent()">تحديث بيانات</button>
                    <button class="btn btn-danger" onclick="deleteStudent()">حذف طالب</button>
                </div>
            </div>
            
            <div class="form-group">
                <h3>إدخال الدرجات</h3>
                
                <div class="form-row">
                    <label>نوع الامتحان:</label>
                    <select id="examType">
                        <option value="">اختر نوع الامتحان</option>
                        <option value="امتحان شهري">امتحان شهري</option>
                        <option value="امتحان نصف الفصل">امتحان نصف الفصل</option>
                        <option value="امتحان نهائي">امتحان نهائي</option>
                        <option value="واجب منزلي">واجب منزلي</option>
                        <option value="مشروع">مشروع</option>
                    </select>
                </div>
                
                <div class="form-row">
                    <label>جزء المادة:</label>
                    <select id="subjectPart">
                        <option value="">اختر جزء المادة</option>
                        <option value="الجزء النظري">الجزء النظري</option>
                        <option value="الجزء العملي">الجزء العملي</option>
                        <option value="المشروع">المشروع</option>
                        <option value="البحث">البحث</option>
                    </select>
                </div>
                
                <div class="form-row">
                    <label>الدرجة المحصلة:</label>
                    <input type="number" id="marks" placeholder="أدخل الدرجة المحصلة" min="0">
                </div>
                
                <div class="form-row">
                    <label>الدرجة الكاملة:</label>
                    <input type="number" id="maxMarks" placeholder="أدخل الدرجة الكاملة" min="1">
                </div>
                
                <div class="form-row">
                    <label>تاريخ الامتحان:</label>
                    <input type="date" id="examDate">
                </div>
                
                <div class="form-row">
                    <label>ملاحظات:</label>
                    <input type="text" id="notes" placeholder="ملاحظات إضافية (اختياري)">
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="addMarks()">إضافة درجة</button>
                    <button class="btn btn-danger" onclick="deleteMarks()">حذف درجة</button>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn btn-info" onclick="generateReport()">تقرير شامل</button>
            <button class="btn btn-success" onclick="exportData()">تصدير البيانات</button>
            <button class="btn btn-warning" onclick="importData()">استيراد البيانات</button>
            <button class="btn btn-primary" onclick="addSampleData()">إضافة بيانات تجريبية</button>
        </div>
        
        <table class="data-table" id="dataTable">
            <thead>
                <tr>
                    <th>اسم الطالب</th>
                    <th>رقم الطالب</th>
                    <th>الصف</th>
                    <th>نوع الامتحان</th>
                    <th>جزء المادة</th>
                    <th>الدرجة</th>
                    <th>الدرجة الكاملة</th>
                    <th>النسبة المئوية</th>
                    <th>تاريخ الامتحان</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody id="tableBody">
            </tbody>
        </table>
    </div>

    <script>
        // تحويل الأرقام إلى الأرقام الهندية
        function toHindiNumbers(num) {
            const hindiDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return num.toString().replace(/\d/g, (digit) => hindiDigits[parseInt(digit)]);
        }
        
        // تحويل الأرقام الهندية إلى الإنجليزية
        function fromHindiNumbers(str) {
            const hindiDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            let result = str;
            hindiDigits.forEach((hindi, index) => {
                result = result.replace(new RegExp(hindi, 'g'), index.toString());
            });
            return result;
        }
        
        // بيانات التطبيق
        let students = JSON.parse(localStorage.getItem('students') || '[]');
        let marks = JSON.parse(localStorage.getItem('marks') || '[]');
        let selectedRow = null;
        
        // حفظ البيانات
        function saveData() {
            localStorage.setItem('students', JSON.stringify(students));
            localStorage.setItem('marks', JSON.stringify(marks));
            updateDisplay();
        }
        
        // عرض رسالة
        function showAlert(message, type = 'success') {
            const alertsDiv = document.getElementById('alerts');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertsDiv.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
            setTimeout(() => {
                alertsDiv.innerHTML = '';
            }, 3000);
        }
        
        // إضافة طالب
        function addStudent() {
            const name = document.getElementById('studentName').value.trim();
            const studentId = document.getElementById('studentId').value.trim();
            const className = document.getElementById('className').value;
            
            if (!name || !studentId || !className) {
                showAlert('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }
            
            // التحقق من عدم تكرار رقم الطالب
            if (students.find(s => s.studentId === studentId)) {
                showAlert('رقم الطالب موجود مسبقاً', 'error');
                return;
            }
            
            students.push({
                name,
                studentId,
                className,
                createdDate: new Date().toISOString()
            });
            
            clearStudentFields();
            saveData();
            showAlert(`تم إضافة الطالب ${name} بنجاح`);
        }
        
        // تحديث بيانات الطالب
        function updateStudent() {
            if (!selectedRow) {
                showAlert('يرجى اختيار طالب من القائمة', 'error');
                return;
            }
            
            const name = document.getElementById('studentName').value.trim();
            const studentId = document.getElementById('studentId').value.trim();
            const className = document.getElementById('className').value;
            
            if (!name || !studentId || !className) {
                showAlert('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }
            
            const oldStudentId = selectedRow.studentId;
            const studentIndex = students.findIndex(s => s.studentId === oldStudentId);
            
            if (studentIndex !== -1) {
                students[studentIndex] = { ...students[studentIndex], name, studentId, className };
                
                // تحديث رقم الطالب في الدرجات
                marks.forEach(mark => {
                    if (mark.studentId === oldStudentId) {
                        mark.studentId = studentId;
                    }
                });
                
                clearStudentFields();
                selectedRow = null;
                saveData();
                showAlert('تم تحديث بيانات الطالب بنجاح');
            }
        }
        
        // حذف طالب
        function deleteStudent() {
            if (!selectedRow) {
                showAlert('يرجى اختيار طالب من القائمة', 'error');
                return;
            }
            
            if (confirm(`هل أنت متأكد من حذف الطالب ${selectedRow.name}؟\nسيتم حذف جميع درجاته أيضاً`)) {
                const studentId = selectedRow.studentId;
                
                // حذف الطالب
                students = students.filter(s => s.studentId !== studentId);
                
                // حذف درجات الطالب
                marks = marks.filter(m => m.studentId !== studentId);
                
                clearStudentFields();
                selectedRow = null;
                saveData();
                showAlert(`تم حذف الطالب بنجاح`);
            }
        }
        
        // إضافة درجة
        function addMarks() {
            const studentId = document.getElementById('studentId').value.trim();
            const examType = document.getElementById('examType').value;
            const subjectPart = document.getElementById('subjectPart').value;
            const marksValue = parseFloat(document.getElementById('marks').value);
            const maxMarksValue = parseFloat(document.getElementById('maxMarks').value);
            const examDate = document.getElementById('examDate').value;
            const notes = document.getElementById('notes').value.trim();
            
            if (!studentId || !examType || !subjectPart || isNaN(marksValue) || isNaN(maxMarksValue) || !examDate) {
                showAlert('يرجى ملء جميع البيانات المطلوبة', 'error');
                return;
            }
            
            if (marksValue > maxMarksValue) {
                showAlert('الدرجة المحصلة لا يمكن أن تكون أكبر من الدرجة الكاملة', 'error');
                return;
            }
            
            if (marksValue < 0 || maxMarksValue <= 0) {
                showAlert('الدرجات يجب أن تكون أرقام موجبة', 'error');
                return;
            }
            
            // التحقق من وجود الطالب
            const student = students.find(s => s.studentId === studentId);
            if (!student) {
                showAlert('رقم الطالب غير موجود', 'error');
                return;
            }
            
            marks.push({
                studentId,
                examType,
                subjectPart,
                marks: marksValue,
                maxMarks: maxMarksValue,
                examDate,
                notes,
                createdDate: new Date().toISOString()
            });
            
            clearMarksFields();
            saveData();
            showAlert(`تم إضافة درجة للطالب ${student.name} بنجاح`);
        }
        
        // حذف درجة
        function deleteMarks() {
            if (!selectedRow || !selectedRow.examType) {
                showAlert('يرجى اختيار درجة من القائمة', 'error');
                return;
            }
            
            if (confirm(`هل أنت متأكد من حذف درجة ${selectedRow.examType} - ${selectedRow.subjectPart} للطالب ${selectedRow.name}؟`)) {
                marks = marks.filter(m => 
                    !(m.studentId === selectedRow.studentId && 
                      m.examType === selectedRow.examType && 
                      m.subjectPart === selectedRow.subjectPart)
                );
                
                selectedRow = null;
                saveData();
                showAlert('تم حذف الدرجة بنجاح');
            }
        }
        
        // مسح حقول الطالب
        function clearStudentFields() {
            document.getElementById('studentName').value = '';
            document.getElementById('studentId').value = '';
            document.getElementById('className').value = '';
        }
        
        // مسح حقول الدرجات
        function clearMarksFields() {
            document.getElementById('examType').value = '';
            document.getElementById('subjectPart').value = '';
            document.getElementById('marks').value = '';
            document.getElementById('maxMarks').value = '';
            document.getElementById('examDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('notes').value = '';
        }
        
        // تحديث العرض
        function updateDisplay() {
            updateTable();
            updateStats();
        }
        
        // تحديث الجدول
        function updateTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            // دمج بيانات الطلاب والدرجات
            const combinedData = [];
            
            students.forEach(student => {
                const studentMarks = marks.filter(m => m.studentId === student.studentId);
                
                if (studentMarks.length === 0) {
                    combinedData.push({
                        ...student,
                        examType: '',
                        subjectPart: '',
                        marks: '',
                        maxMarks: '',
                        percentage: '',
                        examDate: '',
                        notes: ''
                    });
                } else {
                    studentMarks.forEach(mark => {
                        const percentage = ((mark.marks / mark.maxMarks) * 100).toFixed(1);
                        combinedData.push({
                            ...student,
                            ...mark,
                            percentage: percentage + '%'
                        });
                    });
                }
            });
            
            // ترتيب البيانات
            combinedData.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
            
            combinedData.forEach(row => {
                const tr = document.createElement('tr');
                tr.style.cursor = 'pointer';
                tr.onclick = () => selectRow(row, tr);
                
                tr.innerHTML = `
                    <td>${row.name}</td>
                    <td>${toHindiNumbers(row.studentId)}</td>
                    <td>${row.className}</td>
                    <td>${row.examType}</td>
                    <td>${row.subjectPart}</td>
                    <td>${row.marks ? toHindiNumbers(row.marks.toString()) : ''}</td>
                    <td>${row.maxMarks ? toHindiNumbers(row.maxMarks.toString()) : ''}</td>
                    <td>${row.percentage ? toHindiNumbers(row.percentage) : ''}</td>
                    <td>${row.examDate}</td>
                    <td>${row.notes}</td>
                `;
                
                tbody.appendChild(tr);
            });
        }
        
        // اختيار صف
        function selectRow(rowData, trElement) {
            // إزالة التحديد السابق
            document.querySelectorAll('.data-table tr').forEach(tr => {
                tr.style.backgroundColor = '';
            });
            
            // تحديد الصف الحالي
            trElement.style.backgroundColor = '#e3f2fd';
            selectedRow = rowData;
            
            // ملء البيانات في النموذج
            document.getElementById('studentName').value = rowData.name;
            document.getElementById('studentId').value = rowData.studentId;
            document.getElementById('className').value = rowData.className;
            
            if (rowData.examType) {
                document.getElementById('examType').value = rowData.examType;
                document.getElementById('subjectPart').value = rowData.subjectPart;
                document.getElementById('marks').value = rowData.marks;
                document.getElementById('maxMarks').value = rowData.maxMarks;
                document.getElementById('examDate').value = rowData.examDate;
                document.getElementById('notes').value = rowData.notes;
            }
        }
        
        // تحديث الإحصائيات
        function updateStats() {
            const totalStudents = students.length;
            const totalMarks = marks.length;
            
            let totalPercentage = 0;
            let validMarks = 0;
            
            marks.forEach(mark => {
                if (mark.marks && mark.maxMarks && mark.maxMarks > 0) {
                    totalPercentage += (mark.marks / mark.maxMarks) * 100;
                    validMarks++;
                }
            });
            
            const avgGrade = validMarks > 0 ? (totalPercentage / validMarks).toFixed(1) : 0;
            
            document.getElementById('totalStudents').textContent = toHindiNumbers(totalStudents.toString());
            document.getElementById('totalMarks').textContent = toHindiNumbers(totalMarks.toString());
            document.getElementById('avgGrade').textContent = toHindiNumbers(avgGrade.toString()) + '٪';
        }
        
        // إنشاء تقرير
        function generateReport() {
            let report = `تقرير شامل لدرجات الطلاب في مادة تقنية المعلومات\n`;
            report += `تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}\n`;
            report += `${'='.repeat(60)}\n\n`;
            
            report += `إحصائيات عامة:\n`;
            report += `عدد الطلاب المسجلين: ${toHindiNumbers(students.length.toString())}\n`;
            report += `عدد الدرجات المسجلة: ${toHindiNumbers(marks.length.toString())}\n\n`;
            
            report += `تفاصيل الطلاب والدرجات:\n`;
            report += `${'='.repeat(60)}\n`;
            
            students.forEach(student => {
                const studentMarks = marks.filter(m => m.studentId === student.studentId);
                
                report += `\nالطالب: ${student.name}\n`;
                report += `رقم الطالب: ${toHindiNumbers(student.studentId)}\n`;
                report += `الصف: ${student.className}\n`;
                report += `عدد الامتحانات: ${toHindiNumbers(studentMarks.length.toString())}\n`;
                
                if (studentMarks.length > 0) {
                    let totalPercentage = 0;
                    studentMarks.forEach(mark => {
                        totalPercentage += (mark.marks / mark.maxMarks) * 100;
                    });
                    const avgPercentage = (totalPercentage / studentMarks.length).toFixed(1);
                    report += `المعدل العام: ${toHindiNumbers(avgPercentage)}٪\n`;
                    
                    report += `الدرجات:\n`;
                    studentMarks.forEach(mark => {
                        const percentage = ((mark.marks / mark.maxMarks) * 100).toFixed(1);
                        report += `  - ${mark.examType} (${mark.subjectPart}): ${toHindiNumbers(mark.marks.toString())}/${toHindiNumbers(mark.maxMarks.toString())} (${toHindiNumbers(percentage)}٪) - ${mark.examDate}\n`;
                    });
                } else {
                    report += `المعدل العام: لا توجد درجات\n`;
                }
                
                report += `${'-'.repeat(40)}\n`;
            });
            
            // عرض التقرير في نافذة جديدة
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>التقرير الشامل</title>
                    <style>
                        body { font-family: 'Arial Unicode MS', 'Tahoma', sans-serif; direction: rtl; padding: 20px; }
                        pre { white-space: pre-wrap; font-family: inherit; }
                    </style>
                </head>
                <body>
                    <pre>${report}</pre>
                    <button onclick="window.print()">طباعة التقرير</button>
                </body>
                </html>
            `);
        }
        
        // تصدير البيانات
        function exportData() {
            const data = {
                students,
                marks,
                exportDate: new Date().toISOString()
            };
            
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `student_marks_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showAlert('تم تصدير البيانات بنجاح');
        }
        
        // استيراد البيانات
        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            
                            if (confirm('هل أنت متأكد من استيراد البيانات؟\nسيتم استبدال البيانات الحالية')) {
                                students = data.students || [];
                                marks = data.marks || [];
                                saveData();
                                showAlert('تم استيراد البيانات بنجاح');
                            }
                        } catch (error) {
                            showAlert('خطأ في قراءة الملف', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            
            input.click();
        }
        
        // إضافة بيانات تجريبية
        function addSampleData() {
            if (confirm('هل تريد إضافة بيانات تجريبية؟')) {
                const sampleStudents = [
                    {name: "أحمد محمد علي", studentId: "2023001", className: "الصف الأول"},
                    {name: "فاطمة أحمد حسن", studentId: "2023002", className: "الصف الأول"},
                    {name: "محمد عبدالله سالم", studentId: "2023003", className: "الصف الثاني"},
                    {name: "عائشة محمود عبدالرحمن", studentId: "2023004", className: "الصف الثاني"},
                    {name: "عبدالرحمن خالد أحمد", studentId: "2023005", className: "الصف الثالث"}
                ];
                
                const examTypes = ["امتحان شهري", "امتحان نصف الفصل", "امتحان نهائي"];
                const subjectParts = ["الجزء النظري", "الجزء العملي"];
                
                sampleStudents.forEach(student => {
                    if (!students.find(s => s.studentId === student.studentId)) {
                        students.push({...student, createdDate: new Date().toISOString()});
                        
                        // إضافة درجات تجريبية
                        for (let i = 0; i < 3; i++) {
                            const maxMarks = Math.floor(Math.random() * 50) + 50;
                            const marksValue = Math.floor(maxMarks * (0.7 + Math.random() * 0.25));
                            
                            marks.push({
                                studentId: student.studentId,
                                examType: examTypes[Math.floor(Math.random() * examTypes.length)],
                                subjectPart: subjectParts[Math.floor(Math.random() * subjectParts.length)],
                                marks: marksValue,
                                maxMarks: maxMarks,
                                examDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                                notes: Math.random() > 0.5 ? "ممتاز" : "",
                                createdDate: new Date().toISOString()
                            });
                        }
                    }
                });
                
                saveData();
                showAlert('تم إضافة البيانات التجريبية بنجاح');
            }
        }
        
        // تهيئة التطبيق
        function initApp() {
            // تعيين التاريخ الحالي
            document.getElementById('examDate').value = new Date().toISOString().split('T')[0];
            
            // تحديث العرض
            updateDisplay();
            
            showAlert('مرحباً بك في برنامج رصد درجات الطلاب!');
        }
        
        // تشغيل التطبيق عند تحميل الصفحة
        window.onload = initApp;
    </script>
</body>
</html>
