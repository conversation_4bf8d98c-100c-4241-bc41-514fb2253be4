# برنامج رصد درجات الطلاب - مادة تقنية المعلومات

## وصف البرنامج
برنامج شامل لإدارة ورصد درجات الطلاب في مادة تقنية المعلومات، مصمم باللغة العربية مع دعم الأرقام الهندية والكتابة من اليمين إلى اليسار.

## المميزات الرئيسية

### إدارة الطلاب
- إضافة طلاب جدد
- تعديل بيانات الطلاب
- حذف الطلاب
- عرض قائمة شاملة بالطلاب

### إدارة الدرجات
- إضافة درجات للامتحانات المختلفة
- دعم أنواع متعددة من الامتحانات (شهري، نصف فصل، نهائي، واجبات، مشاريع)
- تقسيم المادة إلى أجزاء (نظري، عملي، مشروع، بحث)
- حساب النسب المئوية تلقائياً
- إضافة ملاحظات للدرجات

### التقارير والإحصائيات
- تقرير شامل لجميع الطلاب والدرجات
- حساب المعدلات العامة للطلاب
- عرض الإحصائيات العامة

### النسخ الاحتياطي والاستعادة
- إنشاء نسخ احتياطية من البيانات
- استعادة البيانات من النسخ الاحتياطية
- تصدير البيانات إلى ملفات Excel

## متطلبات التشغيل

### المتطلبات الأساسية
- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### المكتبات المطلوبة
```bash
# المكتبات الأساسية (مدمجة مع Python)
- tkinter (واجهة المستخدم الرسومية)
- sqlite3 (قاعدة البيانات)
- json (النسخ الاحتياطية)
- datetime (التواريخ)

# المكتبات الاختيارية (للتصدير إلى Excel)
- pandas
- openpyxl
```

## طريقة التثبيت والتشغيل

### 1. تثبيت المكتبات الاختيارية
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python student_marks_app.py
```

## طريقة الاستخدام

### إضافة طالب جديد
1. أدخل اسم الطالب في حقل "اسم الطالب"
2. أدخل رقم الطالب في حقل "رقم الطالب"
3. اختر الصف من القائمة المنسدلة
4. اضغط على زر "إضافة طالب"

### إضافة درجة
1. أدخل رقم الطالب أو اختر طالب من الجدول
2. اختر نوع الامتحان من القائمة المنسدلة
3. اختر جزء المادة من القائمة المنسدلة
4. أدخل الدرجة المحصلة والدرجة الكاملة
5. أدخل تاريخ الامتحان (أو استخدم التاريخ الحالي)
6. أضف ملاحظات إذا لزم الأمر
7. اضغط على زر "إضافة درجة"

### عرض التقارير
- اضغط على زر "تقرير شامل" لعرض تقرير مفصل
- يمكن حفظ التقرير أو طباعته

### النسخ الاحتياطي
- اضغط على زر "نسخ احتياطي" لحفظ البيانات
- اضغط على زر "استعادة البيانات" لاستعادة نسخة احتياطية

### التصدير إلى Excel
- اضغط على زر "تصدير إلى Excel" لحفظ البيانات في ملف Excel

## الملفات المُنشأة

### قاعدة البيانات
- `student_marks.db`: ملف قاعدة البيانات الرئيسي

### النسخ الاحتياطية
- ملفات JSON تحتوي على جميع البيانات

### التقارير
- ملفات نصية تحتوي على التقارير المُنشأة

## المميزات التقنية

### الواجهة العربية
- دعم كامل للغة العربية
- اتجاه الكتابة من اليمين إلى اليسار
- استخدام الأرقام الهندية (٠-٩)
- خطوط عربية واضحة

### قاعدة البيانات
- استخدام SQLite لحفظ البيانات
- جداول منفصلة للطلاب والدرجات
- ضمان سلامة البيانات

### الأمان
- التحقق من صحة البيانات المُدخلة
- رسائل تأكيد للعمليات الحساسة
- نسخ احتياطية آمنة

## الدعم والمساعدة

### رسائل الخطأ
البرنامج يعرض رسائل خطأ واضحة باللغة العربية لمساعدة المستخدم

### التحقق من البيانات
- التأكد من ملء جميع الحقول المطلوبة
- التحقق من صحة الأرقام المُدخلة
- منع إدخال درجات أكبر من الدرجة الكاملة

## الترخيص
هذا البرنامج مجاني ومفتوح المصدر للاستخدام التعليمي.

## المطور
تم تطوير هذا البرنامج باستخدام Python و Tkinter لخدمة المعلمين والطلاب في مادة تقنية المعلومات.
